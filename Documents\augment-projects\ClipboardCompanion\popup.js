/**
 * Clipboard Companion - Popup Settings JavaScript
 * Handles settings form, statistics display, and user interactions
 */

// DOM elements
const settingsForm = document.getElementById('settingsForm');
const historyLimitInput = document.getElementById('historyLimit');
const saveBtn = document.getElementById('saveBtn');
const addClipboardBtn = document.getElementById('addClipboardBtn');
const clearBtn = document.getElementById('clearBtn');
const statusMessage = document.getElementById('statusMessage');
const currentCountElement = document.getElementById('currentCount');
const storageUsedElement = document.getElementById('storageUsed');

// Default settings
const DEFAULT_SETTINGS = {
    historyLimit: 25,
    shortcut: 'Ctrl+Shift+X'
};

/**
 * Initialize popup when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', async () => {
    await loadSettings();
    await updateStatistics();
    setupEventListeners();
});

/**
 * Load current settings from storage and populate form
 */
async function loadSettings() {
    try {
        const result = await chrome.storage.local.get(['settings']);
        const settings = result.settings || DEFAULT_SETTINGS;
        
        // Populate form fields
        historyLimitInput.value = settings.historyLimit || DEFAULT_SETTINGS.historyLimit;
        
        console.log('Settings loaded:', settings);
    } catch (error) {
        console.error('Error loading settings:', error);
        showStatusMessage('Error loading settings', 'error');
    }
}

/**
 * Update statistics display with current clipboard data
 */
async function updateStatistics() {
    try {
        const result = await chrome.storage.local.get(['clipboardHistory']);
        const history = result.clipboardHistory || [];
        
        // Update item count
        currentCountElement.textContent = history.length;
        
        // Calculate storage usage (approximate)
        const storageSize = JSON.stringify(history).length;
        const storageSizeKB = Math.round(storageSize / 1024 * 10) / 10; // Round to 1 decimal
        storageUsedElement.textContent = storageSizeKB;
        
        console.log('Statistics updated:', { count: history.length, sizeKB: storageSizeKB });
    } catch (error) {
        console.error('Error updating statistics:', error);
        currentCountElement.textContent = '?';
        storageUsedElement.textContent = '?';
    }
}

/**
 * Setup event listeners for form interactions
 */
function setupEventListeners() {
    // Settings form submission
    settingsForm.addEventListener('submit', handleSaveSettings);

    // Add clipboard button
    addClipboardBtn.addEventListener('click', handleAddClipboard);

    // Clear history button
    clearBtn.addEventListener('click', handleClearHistory);

    // Input validation
    historyLimitInput.addEventListener('input', validateHistoryLimit);

    // Real-time statistics updates
    setInterval(updateStatistics, 2000);
}

/**
 * Handle settings form submission
 */
async function handleSaveSettings(event) {
    event.preventDefault();
    
    try {
        // Validate input
        const historyLimit = parseInt(historyLimitInput.value);
        if (isNaN(historyLimit) || historyLimit < 5 || historyLimit > 100) {
            showStatusMessage('History limit must be between 5 and 100', 'error');
            return;
        }
        
        // Prepare settings object
        const settings = {
            historyLimit: historyLimit,
            shortcut: 'Ctrl+Shift+X' // Fixed for now, can be made configurable later
        };
        
        // Show loading state
        saveBtn.disabled = true;
        saveBtn.querySelector('.btn-text').textContent = 'Saving...';
        
        // Save to storage
        await chrome.storage.local.set({ settings });
        
        // Trim history if new limit is smaller
        await trimHistoryToLimit(historyLimit);
        
        // Show success message
        showStatusMessage('Settings saved successfully!', 'success');
        
        // Update statistics
        await updateStatistics();
        
        console.log('Settings saved:', settings);
        
    } catch (error) {
        console.error('Error saving settings:', error);
        showStatusMessage('Error saving settings', 'error');
    } finally {
        // Reset button state
        saveBtn.disabled = false;
        saveBtn.querySelector('.btn-text').textContent = 'Save Settings';
    }
}

/**
 * Handle add text manually button click
 */
async function handleAddClipboard() {
    // Show a simple prompt for user to paste content manually
    const clipboardText = prompt('Paste your text here (Ctrl+V):');

    if (!clipboardText || !clipboardText.trim()) {
        showStatusMessage('No content provided', 'error');
        return;
    }

    try {
        // Send to background script to add to history
        chrome.runtime.sendMessage({
            action: 'addToClipboard',
            text: clipboardText.trim()
        }, (response) => {
            if (response && response.success) {
                showStatusMessage('Text added successfully!', 'success');
                updateStatistics(); // Refresh statistics
            } else {
                showStatusMessage('Failed to add text', 'error');
            }
        });

        console.log('Manually added text:', clipboardText.substring(0, 50) + '...');

    } catch (error) {
        console.error('Error adding text:', error);
        showStatusMessage('Error adding text', 'error');
    }
}

/**
 * Handle clear history button click
 */
async function handleClearHistory() {
    try {
        // Show confirmation
        const confirmed = confirm('Are you sure you want to clear all clipboard history? This action cannot be undone.');
        
        if (!confirmed) {
            return;
        }
        
        // Show loading state
        clearBtn.disabled = true;
        clearBtn.querySelector('.btn-text').textContent = 'Clearing...';
        
        // Clear history
        await chrome.storage.local.set({ clipboardHistory: [] });
        
        // Show success message
        showStatusMessage('Clipboard history cleared', 'success');
        
        // Update statistics
        await updateStatistics();
        
        console.log('Clipboard history cleared');
        
    } catch (error) {
        console.error('Error clearing history:', error);
        showStatusMessage('Error clearing history', 'error');
    } finally {
        // Reset button state
        clearBtn.disabled = false;
        clearBtn.querySelector('.btn-text').textContent = 'Clear History';
    }
}

/**
 * Validate history limit input
 */
function validateHistoryLimit() {
    const value = parseInt(historyLimitInput.value);
    const isValid = !isNaN(value) && value >= 5 && value <= 100;
    
    if (historyLimitInput.value && !isValid) {
        historyLimitInput.style.borderColor = '#e53e3e';
        historyLimitInput.style.backgroundColor = '#fed7d7';
    } else {
        historyLimitInput.style.borderColor = '';
        historyLimitInput.style.backgroundColor = '';
    }
}

/**
 * Trim clipboard history to specified limit
 */
async function trimHistoryToLimit(limit) {
    try {
        const result = await chrome.storage.local.get(['clipboardHistory']);
        const history = result.clipboardHistory || [];
        
        if (history.length > limit) {
            const trimmedHistory = history.slice(0, limit);
            await chrome.storage.local.set({ clipboardHistory: trimmedHistory });
            console.log(`History trimmed from ${history.length} to ${limit} items`);
        }
    } catch (error) {
        console.error('Error trimming history:', error);
    }
}

/**
 * Show status message to user
 */
function showStatusMessage(message, type = 'success') {
    statusMessage.textContent = message;
    statusMessage.className = `status-message ${type}`;
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
        statusMessage.className = 'status-message hidden';
    }, 3000);
}

/**
 * Calculate approximate storage size in bytes
 */
function calculateStorageSize(data) {
    return new Blob([JSON.stringify(data)]).size;
}
