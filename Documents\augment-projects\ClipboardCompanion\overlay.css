/**
 * Clipboard Companion - Overlay Styles
 * Modern, clean interface with tag cloud and sticky note interactions
 */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap');

/* Overlay Container - Allow clicks to pass through except on content */
.cc-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 2147483647; /* Maximum z-index */
    display: none;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none; /* Allow clicks to pass through */
}

.cc-overlay.cc-visible {
    opacity: 1;
    pointer-events: none; /* Keep overlay non-blocking even when visible */
}

/* Backdrop - Completely transparent and non-blocking */
.cc-overlay-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    pointer-events: none;
}

/* Main Content Container - Compact and efficient */
.cc-overlay-content {
    position: absolute;
    width: 500px;
    height: 350px;
    background: #f8f9fa;
    border-radius: 12px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
    cursor: default;
    /* Center initially */
    top: 50%;
    left: 50%;
    margin-top: -175px;
    margin-left: -250px;
    border: 1px solid #e2e8f0;
    pointer-events: auto; /* Enable interactions with overlay content */
}

.cc-overlay.cc-visible .cc-overlay-content {
    transform: scale(1);
}

/* Make content draggable but prevent text selection during drag */
.cc-overlay-content.cc-dragging {
    user-select: none;
    cursor: move !important;
}

.cc-overlay-content.cc-dragging * {
    pointer-events: none;
}

/* Header - Compact and draggable */
.cc-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: #ffffff;
    border-bottom: 1px solid #e2e8f0;
    cursor: move;
    user-select: none;
    border-radius: 12px 12px 0 0;
}

.cc-header:hover {
    background: #f8f9fa;
}

.cc-title {
    font-size: 16px;
    font-weight: 600;
    color: #1a202c;
    margin: 0;
    pointer-events: none;
}

.cc-refresh-btn, .cc-close-btn {
    background: none;
    border: none;
    font-size: 20px;
    color: #718096;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    line-height: 1;
    pointer-events: auto;
    z-index: 10;
    margin-left: 8px;
}

.cc-close-btn {
    font-size: 24px;
}

.cc-refresh-btn:hover, .cc-close-btn:hover {
    background: #f7fafc;
    color: #2d3748;
    transform: scale(1.1);
}

/* Tag Cloud Container - More compact */
.cc-tag-cloud {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-content: flex-start;
    justify-content: center;
    position: relative;
}

/* Empty State */
.cc-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #718096;
    text-align: center;
}

.cc-empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
}

.cc-empty-state p {
    font-size: 16px;
    font-weight: 500;
    margin: 0 0 8px 0;
}

.cc-empty-state small {
    font-size: 14px;
    opacity: 0.8;
}

/* Clipboard Tags - Compact single line */
.cc-tag {
    background: #FFF9C4; /* Default yellow - will be overridden by JS */
    color: #2d3748;
    padding: 8px 12px;
    border-radius: 16px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;
    border: 1px solid rgba(0, 0, 0, 0.1);
    max-width: 180px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.2;
    position: relative;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    height: auto;
    min-height: 32px;
    display: flex;
    align-items: center;
}

.cc-tag:hover {
    filter: brightness(0.95) saturate(1.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: rgba(0, 0, 0, 0.2);
}

.cc-tag:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cc-tag.cc-dragging {
    opacity: 0.8;
    transform: rotate(5deg) scale(1.05);
    z-index: 1000;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    border-color: #4299e1;
    background: #4299e1;
    color: white;
}

/* Organic tag cloud layout variations */
.cc-tag:nth-child(3n+1) {
    transform: rotate(-1deg);
}

.cc-tag:nth-child(3n+2) {
    transform: rotate(1deg);
}

.cc-tag:nth-child(3n+3) {
    transform: rotate(-0.5deg);
}

.cc-tag:nth-child(4n+1) {
    margin-top: 8px;
}

.cc-tag:nth-child(4n+3) {
    margin-top: -4px;
}

/* Hover resets rotation for better readability */
.cc-tag:hover {
    transform: translateY(-2px) rotate(0deg) !important;
}

/* Sticky Note */
.cc-sticky-note {
    position: fixed;
    display: none;
    max-width: 250px;
    min-width: 200px;
    padding: 16px;
    background: #FFF9C4; /* Default yellow */
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
    font-size: 13px;
    line-height: 1.5;
    color: #2d3748;
    z-index: 2147483648; /* Above overlay */
    transform: scale(0.8) rotate(-2deg);
    opacity: 0;
    transition: all 0.2s ease;
    word-wrap: break-word;
    white-space: pre-wrap;
    pointer-events: auto; /* Allow hover events */
    cursor: default;
}

.cc-sticky-note.cc-visible {
    transform: scale(1) rotate(-1deg);
    opacity: 1;
}

.cc-sticky-note::before {
    content: '';
    position: absolute;
    top: -1px;
    left: 20px;
    width: 20px;
    height: 20px;
    background: inherit;
    border: inherit;
    border-bottom: none;
    border-right: none;
    transform: rotate(45deg);
    z-index: -1;
}

/* Scrollbar Styling */
.cc-tag-cloud::-webkit-scrollbar {
    width: 8px;
}

.cc-tag-cloud::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.cc-tag-cloud::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
}

.cc-tag-cloud::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cc-overlay-content {
        width: 90%;
        height: 80%;
        /* Adjust positioning for smaller screens */
        top: 10%;
        left: 5%;
        margin: 0;
    }

    .cc-header {
        padding: 14px 18px;
    }

    .cc-title {
        font-size: 16px;
    }

    .cc-tag-cloud {
        padding: 16px;
        gap: 8px;
    }

    .cc-tag {
        padding: 10px 14px;
        font-size: 13px;
        max-width: 150px;
    }

    .cc-sticky-note {
        max-width: 200px;
        min-width: 150px;
        padding: 12px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .cc-overlay-content {
        width: 95%;
        height: 85%;
        top: 5%;
        left: 2.5%;
        margin: 0;
        border-radius: 12px;
    }

    .cc-header {
        padding: 12px 16px;
    }

    .cc-tag-cloud {
        padding: 12px;
    }

    .cc-tag {
        padding: 8px 12px;
        font-size: 12px;
        max-width: 120px;
    }
}

/* Window constraints for very small screens */
@media (max-width: 600px) {
    .cc-overlay-content {
        min-width: 300px;
        min-height: 400px;
    }
}

/* Animation keyframes */
@keyframes tagPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.cc-tag:active {
    animation: tagPulse 0.2s ease;
}

/* Drag and drop visual feedback */
.cc-tag-cloud.drag-over {
    background: rgba(66, 153, 225, 0.1);
}

/* Focus styles for accessibility */
.cc-tag:focus {
    outline: 2px solid #4299e1;
    outline-offset: 2px;
}

.cc-close-btn:focus {
    outline: 2px solid #4299e1;
    outline-offset: 2px;
}
